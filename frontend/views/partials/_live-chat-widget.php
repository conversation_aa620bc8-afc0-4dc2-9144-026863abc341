<?php
/* @var $this yii\web\View */
/* @var $liveChatGroup array */
/* @var $compact bool */

use common\helpers\ContentHelper;
use frontend\assets\AppAsset;
use yii\helpers\BaseStringHelper;
use yii\helpers\Html;

$compact = $compact ?? false;
$widgetClass = $compact ? 'live-chat-widget-compact' : 'live-chat-widget';

$group = $liveChatGroup['liveChatGroup'];
$activeUsers = $liveChatGroup['activeUsersCount'];

$loggedInUser = Yii::$app->user->identity;

// All messages for chat window (ASC order so chat reads naturally)
$allMessages = $liveChatGroup['allMessages'];

//get Online user
$onlineUsers = $liveChatGroup['onlineUsers'];

// Messages for previews/names (DESC to get most recent first)
$latestMessages = $liveChatGroup['latestMessages'];

// Extract unique user names (ignoring Admin + logged in user)
$recentUserNames = [];
foreach ($onlineUsers as $user) {
    $name = $user['name'];
    $id = $user['user_id'];
    if (strtolower($name) !== 'admin' &&
        (!$loggedInUser || $id !== $loggedInUser->id) &&
        !in_array($name, $recentUserNames)
    ) {
        $recentUserNames[] = $name;
    }
    if (count($recentUserNames) >= 3) {
        break;
    }
}

// Unique latest messages for preview
$uniqueMessages = [];
foreach ($latestMessages as $msg) {
    if (strtolower($msg['user_name']) !== 'admin' &&
        (!$loggedInUser || $msg['user_name'] !== $loggedInUser->username) &&
        !isset($uniqueMessages[$msg['user_name']])
    ) {
        $uniqueMessages[$msg['user_name']] = $msg;
    }
    if (count($uniqueMessages) >= 4) {
        break;
    }
}

$displayMessages = array_values($uniqueMessages);
?>

<div class="pageMask" style="display: none;"></div>
<!-- Enhanced Live Chat Widget -->
<div id="enhanced-chat-widget" class="<?= $widgetClass ?>">
    <!-- Chat Header with Stats -->
    <div class="chat-header-enhanced">
        <div class="header-top-row">
            <div class="header-left">
                <svg width="42" height="18" viewBox="0 0 42 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <rect width="42" height="18" rx="9" fill="#FF4E53" />
                    <path d="M20.2227 11.5101V12.2H17.0234V11.5101H20.2227ZM17.1904 5.80157V12.2H16.3423V5.80157H17.1904ZM22.1035 5.80157V12.2H21.2554V5.80157H22.1035ZM25.7114 11.2288L27.5967 5.80157H28.5151L26.1465
                    12.2H25.4917L25.7114 11.2288ZM23.9492 5.80157L25.8169 11.2288L26.0498 12.2H25.395L23.0308 5.80157H23.9492ZM33.4414 11.5101V12.2H30.0532V11.5101H33.4414ZM30.2246 5.80157V12.2H29.3765V5.80157H30.2246ZM32.9932
                    8.55255V9.24249H30.0532V8.55255H32.9932ZM33.3975 5.80157V6.49591H30.0532V5.80157H33.3975Z" fill="white" />
                    <circle cx="8.99961" cy="8.99998" r="2.4" fill="white" />
                </svg>

                <h4 class="chat-title"><?= BaseStringHelper::truncateWords($group['group_name'], 3) ?></h4>
                <span class="chat-subtitle">Live Chat active</span>
            </div>

            <div class="chat-stats">
                <div class="stat-item">
                    <img src="/yas/images/live-user-icon.svg" alt="Users" class="stat-icon">
                    <div class="stat-number" id="online-users-count">0</div>
                    <div class="stat-label">Total Users</div>
                </div>
                <div class="stat-item">
                    <svg width="12" height="12" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <rect x="4" y="4" width="4" height="4" rx="2" fill="#05EC1C" />
                    </svg>
                    <div class="stat-number"><?= $activeUsers ?></div>
                    <div class="stat-label">Active</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Chat Content Area -->
    <div class="chat-content" id="chat-content">
        <!-- Initial State: Lead Form for Logged Out Users -->
        <div class="chat-lead-form" id="chat-lead-form">
            <div class="chat-horizontal-layout">
                <div class="recent-messages-section">
                    <h5>Recent Messages</h5>
                    <div class="message-preview">
                        <?php foreach ($displayMessages as $message): ?>
                            <div class="preview-item">
                                <strong><?= $message['user_name'] ?></strong>
                                <span class="time"><?= Yii::$app->formatter->asRelativeTime($message['created_at']) ?></span>
                                <p class="preview-text-live-user"><?= $message['message_text'] ?></p>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>

                <div class="join-form-section">
                    <!-- <div class="join-chat-button">
                        <button type="button" class="btn-join-chat" id="join-chat-form-btn">Join Chat</button>
                    </div> -->
                    <p class="students-count" id="online-users-discussion-count">
                        <strong>0 students</strong> discussing
                    </p>

                    <form class="lead-form" id="chat-lead-form-inputs" onsubmit="return false;">
                        <div class="form-group-live-chat">
                            <input type="text" placeholder="Name" class="form-control" id="lead-name" autocomplete="off" maxlength="50" required>
                            <svg width="13" height="15" viewBox="0 0 13 15" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M0.015122 13.6375C0.0466687 13.3722 0.0709354 13.1042 0.112189 12.8417C0.430083 10.914 1.3231 9.40737 2.74027 8.27261C3.72065 9.31987 4.87574 9.87222 6.22497 9.86948C7.56935 9.86948 8.72201 
                                9.32261 9.7121 8.26714C9.90866 8.44214 10.1149 8.60893 10.3042 8.79761C11.4981 9.99253 12.2019 11.4828 12.3936 13.2738C12.4057 13.3941 12.4227 13.5171 12.4372 13.6375C12.4372 13.7824 12.4372 13.93 12.4372 
                                14.075C8.29492 14.075 4.15502 14.075 0.0126953 14.075C0.015122 13.93 0.015122 13.7824 0.015122 13.6375Z" fill="#D9D9D9" />
                                <path d="M6.5186 0.0749512C6.76369 0.129639 7.01364 0.165186 7.25145 0.241748C8.70746 0.698389 9.7849 2.15034 9.93535 3.84839C10.144 6.20269 8.61039 8.26987 6.51132 
                                8.46402C4.62094 8.64175 2.88102 7.12417 2.5607 5.01597C2.21611 2.76284 3.46585 0.662842 5.44844 0.16792C5.6086 0.126904 5.77361 0.105029 5.9362 0.0749512C6.13033 0.0749512 6.32447 0.0749512 6.5186 0.0749512Z" fill="#D9D9D9" />
                            </svg>
                        </div>

                        <div class="form-group-live-chat">
                            <input type="email" placeholder="Email Address" class="form-control" id="lead-email" autocomplete="off" maxlength="50">
                            <svg width="14" height="10" viewBox="0 0 14 10" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path fill-rule="evenodd" clip-rule="evenodd" d="M13.7512 1.08289L9.43329 4.68351L13.7512 8.28412V1.08289ZM13.5354 8.84473C13.4151 8.95637 13.2543 9.02521 13.0791 9.02521H1.53833C1.36364 9.02521 1.20342 8.95695 1.08369 8.84648L5.63184 
                                5.05381L6.32652 5.6334C6.60012 5.8613 6.95816 5.97467 7.31447 5.97294C7.67019 5.9712 8.02882 5.85552 8.30415 5.6253L8.98958 5.05381L13.5354 8.84473ZM0.866211 8.28714L5.18825 4.68356L0.866211 1.07999V8.28714ZM1.0837 0.520707C1.20344 0.40965 
                                1.36366 0.34198 1.53834 0.34198H13.0791C13.2549 0.34198 13.4157 0.410812 13.5354 
                                0.522455L8.81153 4.46201L8.80517 4.46722L7.94101 5.18793C7.76981 5.3308 7.5419 5.40253 7.31285 5.40368C7.08436 5.40484 6.85821 5.33543 6.69047 5.19603L5.81706 4.4678L5.80954 4.46143L1.0837 0.520707Z" fill="#D9D9D9" />
                            </svg>
                        </div>

                        <div class="form-group-live-chat mobileNumberCode">
                            <div class="dialCodeDiv">
                                <img src="/yas/images/indian_flag.png" alt="">
                                <span class="dialCode">+91</span>
                            </div>
                            <input type="tel" placeholder="Mobile Number" class="form-control" id="lead-mobile" maxlength="10" autocomplete="off" maxlength="50" required>
                        </div>

                        <div class="form-control inputStreamLiveContainer modalInputContainer streamLiveClass streamLiveCategory inputStreamLive">
                            <select class="inputContainerField select2HookClass streamLive data-gtm data-gtm-change" data-user-input="streamLive" id="interested_stream_live_lead" name="inputStreamLive">
                                <option></option>
                            </select>
                        </div>
                        <div class="form-control inputLevelLiveContainer modalInputContainer levelLiveClass levelLiveCategory inputLevelLive">
                            <select class="inputContainerField select2HookClass levelLive data-gtm data-gtm-change" data-user-input="levelLive" id="interested_level_live_lead" name="inputLevelLive" disabled>
                                <option></option>
                            </select>
                        </div>

                        <button type="button" class="btn btn-primary join-chat-submit" disabled id="join-chat-form-btn-submit">Join Chat
                            <svg width="12" height="8" viewBox="0 0 12 8" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M11.4161 4.4285C11.6113 4.23324 11.6113 3.91666 11.4161 3.7214L8.23407 0.539417C8.03881 0.344155 7.72223 0.344155 7.52697 0.539417C7.3317 0.734679 7.3317 1.05126 7.52697 1.24652L10.3554 4.07495L7.52697 
                                6.90338C7.3317 7.09864 7.3317 7.41522 7.52697 7.61049C7.72223 7.80575 8.03881 7.80575 8.23407 7.61049L11.4161 4.4285ZM0.9375 4.07495V4.57495H11.0625V4.07495V3.57495H0.9375V4.07495Z" fill="white" />
                            </svg>
                        </button>
                    </form>

                    <form class="otp-box lead-form" id="otp-box-live-chat" style="display: none;" onsubmit="return false;">
                        <input type="text" placeholder="Enter OTP" id="otp-input-live-chat" autocomplete="off" maxlength="4">
                        <span class="otplabel" style="display: none;">OTP sent to 7322348892</span>
                        <button class="verify-btn" id="verify-btn-live-chat" disabled>Verify OTP
                            <svg width="12" height="8" viewBox="0 0 12 8" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M11.4161 4.4285C11.6113 4.23324 11.6113 3.91666 11.4161 3.7214L8.23407 0.539417C8.03881 0.344155 7.72223 0.344155 7.52697 0.539417C7.3317 0.734679 7.3317 1.05126 7.52697 1.24652L10.3554 
                                4.07495L7.52697 6.90338C7.3317 7.09864 7.3317 7.41522 7.52697 7.61049C7.72223 7.80575 8.03881 7.80575 8.23407 7.61049L11.4161 4.4285ZM0.9375 4.07495V4.57495H11.0625V4.07495V3.57495H0.9375V4.07495Z" fill="white" />
                            </svg>
                        </button>
                        <a href="#" class="change-number" id="change-number-btn">Change Number</a>
                    </form>
                </div>
            </div>
        </div>

        <!-- Chat Interface (Hidden Initially) -->
        <div class="chat-interface" id="chat-interface" style="display: none;">
            <!-- Live Chat Messages Interface -->
            <div class="live-chat-messages" id="live-chat-messages">
                <!-- Chat Tabs -->
                <div class="chat-tabs-container">
                    <div class="chat-tabs">
                        <span class="tab active" data-tab="messages">
                            <!-- <i class="fa fa-comment"></i>  -->
                            <svg width="15" height="14" viewBox="0 0 15 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M2.6985 9.71575C1.875 9.05706 1.875 8.55869 1.875 6.4375C1.875 4.31631 1.875 3.25544 2.6985 2.59675C3.52312 1.9375 4.84837 1.9375 7.5 1.9375C10.1516 1.9375 11.4774 1.9375 
                                12.3009 2.59675C13.1244 3.256 13.125 4.31631 13.125 6.4375C13.125 8.55869 13.125 9.05706 12.3009 9.71575C11.478 10.375 10.1516 10.375 7.5 10.375C6.08813 10.375 5.3625 11.3526 4.125 12.0625V10.2558C3.50963 
                                10.1641 3.05681 10.0026 2.6985 9.71575Z" stroke="#020817" stroke-opacity="0.8" stroke-width="0.84375" stroke-linecap="round" stroke-linejoin="round" />
                            </svg>

                            <p class="tab-label-active">Messages</p>
                            <div class="badge"><?= count($allMessages) ?></div>
                        </span>
                    </div>
                </div>

                <!-- Welcome Message -->
                <?php if (!empty($recentUserNames)): ?>
                    <div class="welcome-notification">
                        <p class="welcome-text-user-names" id="welcome-text-user-names"><strong><?= implode(', ', $recentUserNames) ?></strong> joined the chat</p>
                    </div>
                <?php else: ?>
                    <div class="welcome-notification">
                        <p class="welcome-text-user-names" id="welcome-text-user-names">Welcome to the chat!</p>
                    </div>
                <?php endif; ?>

                <!-- Messages Container -->

                <div class="messages-container" id="messages-container">
                    <?php
                    $palette = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#FFA726', '#AB47BC', '#26A69A'];

                    foreach ($allMessages as $msg):
                        $userName = trim($msg['user_name'] ?? '');
                        if ($userName === '' || $userName === 'undefined') {
                            continue;
                        }

                        if ($msg['message_type'] === 'file') {
                            $attachments = json_decode($msg['attachments'] ?? '', true);
                            if (empty($attachments) || !is_array($attachments)) {
                                continue;
                            }
                        }

                        if (in_array($msg['message_type'], ['text', 'link'], true) && trim($msg['message_text'] ?? '') === '') {
                            continue;
                        }

                        $isCurrentUser = ($loggedInUser && $loggedInUser->id == $msg['user_id']);
                        $isAdmin       = $msg['is_admin_message'];
                        $username      = htmlspecialchars($msg['user_name']);

                        $messageTime = new DateTime($msg['created_at']);
                        $isoTimestamp = $messageTime->format('c'); // ISO 8601 format
                        $displayTime = $messageTime->format('h:i A');

                        $messageText   = $msg['message_type'] === 'text' ? nl2br(ContentHelper::removeStyleTag(stripslashes(html_entity_decode($msg['message_text'])))) : '';
                        $initials      = strtoupper(substr($username, 0, 2));

                        $displayName   = $isCurrentUser ? '' : $username;
                        $wrapperClass  = $isAdmin ? 'message moderator-message' : 'message user-message';
                        $textClass     = $isAdmin ? 'message-text moderator-text' : 'message-text';
                        $highlight     = $isCurrentUser ? 'highlighted' : '';

                        $avatarStyle   = $isAdmin
                            ? 'background:#FF4E53;color:#fff;'
                            : 'background:' . $palette[crc32($username) % count($palette)] . ';color:#fff;';

                        // attachments
                        $attachmentsHtml = '';
                        if ($msg['message_type'] === 'file' && !empty($msg['attachments'])) {
                            foreach ((array) json_decode($msg['attachments'], true) as $file) {
                                $fileName = htmlspecialchars($file['name']);
                                $fileUrl  = $file['url'];
                                $fileType = isset($file['type']) ? $file['type'] : '';
                                $isImage = preg_match('/\.(jpg|jpeg|png|gif|webp|svg)$/i', $fileName) ||
                                    strpos($fileType, 'image/') === 0;

                                $icon = $isImage ? '🖼️' : '📂';
                                $downloadAttr = $isImage ? '' : 'download="' . $fileName . '"';

                                $attachmentsHtml .= "
                    <div class='message-text attachment'>
                        <span class='chat-attachment-link-icon'>{$icon}</span> 
                       <a href='{$fileUrl}' target='_blank' class='chat-attachment-link' {$downloadAttr}>
                                <span class='filename'>{$fileName}</span>
                            </a>
                    </div>";
                            }
                        }
                        ?>
                        <div class="chat-message <?= $highlight ?>" data-timestamp="<?= $isoTimestamp ?>">

                            <?php if (!$isCurrentUser && $displayName): ?>
                                <div class="message-avatar-header">
                                    <div class="message-avatar" style="<?= $avatarStyle ?>"><?= $initials ?></div>
                                    <div class="message-header">
                                        <span class="username"><?= $displayName ?></span>
                                        <?php if ($isAdmin): ?>
                                            <span class="badge mod-badge">Mod</span>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            <?php endif; ?>
                            <div class="message-content">
                                <div class="message-bubble">
                                    <div class="common-text-box <?= $textClass ?>">
                                        <?php if (!empty($messageText)): ?>
                                            <?= $messageText ?>
                                        <?php endif; ?>

                                        <?php if (!empty($attachmentsHtml)): ?>
                                            <?= $attachmentsHtml ?>
                                        <?php endif; ?>

                                        <?php if ($msg['message_type'] === 'link'): ?>
                                            <a class="chat-user-link" href="<?= htmlspecialchars($msg['message_text']) ?>" target="_blank">
                                                <?= htmlspecialchars($msg['message_text']) ?>
                                            </a>
                                        <?php endif; ?>
                                    </div>
                                </div>

                                <div class="message-timestamp">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none"
                                        stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                                        class="lucide lucide-clock">
                                        <circle cx="12" cy="12" r="10"></circle>
                                        <polyline points="12 6 12 12 16 14"></polyline>
                                    </svg>
                                    <?= $displayTime ?>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>

                <!-- Message Input -->
                <div class="message-input-container">
                    <div class="input-wrapper">
                        <button class="attachment-btn" title="Attach file">
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M19 12.998H13V18.998H11V12.998H5V10.998H11V4.99805H13V10.998H19V12.998Z" fill="#757575" />
                            </svg>
                        </button>
                        <input type="file" id="file-input-live-chat" style="display:none;" multiple />
                        <div class="input-with-emoji">
                            <input type="text" placeholder="Type your message..." class="message-input" id="message-input-enhanced">
                            <!-- <button class="emoji-btn" title="Add emoji">
                                <i class="fa fa-smile-o"></i>
                            </button> -->
                        </div>
                        <button class="send-btn" id="send-btn-enhanced" title="Send message" disabled>
                            <!-- <i class="fa fa-send"></i> -->
                            <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M14.0318 2.03524C12.5798 0.471238 1.65718 4.3019 1.66651 5.6999C1.67651 7.2859 5.93184 7.77324 7.11118 8.10457C7.82051 8.3039 8.01051 8.50724 8.17384 9.25124C8.91451 12.6192 9.28718 14.2952 10.1338 14.3326C11.4852 14.3926 15.4485 3.56124 14.0318 2.03524Z" stroke="white" />
                                <path d="M7.6665 8.33333L9.99984 6" stroke="white" stroke-linecap="round" stroke-linejoin="round" />
                            </svg>
                        </button>
                    </div>
                </div>
            </div>
            <p id="input-hint-text">Max File Size: 10KB, Max 5 files</p>
        </div>
    </div>
</div>

<!-- Chat Guidelines Modal (Hidden by default) -->
<div class="modal fade" id="chatGuidelinesModal" tabindex="-1" role="dialog" style="display: none;">
    <div class="modal-dialog modal-md" role="document">
        <div class="modal-content chat-guidelines-modal">
            <div class="modal-header">
                <button type="button" class="guidelinesClose" data-dismiss="modal" aria-label="Close">
                    <!-- <span aria-hidden="true">&times;</span> -->
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="#000"
                        stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-x h-4 w-4" data-lov-id="src/components/ui/dialog.tsx:46:8" data-lov-name="X" data-component-path="src/components/ui/dialog.tsx" data-component-line="46" data-component-file="dialog.tsx" data-component-name="X" data-component-content="%7B%22className%22%3A%22h-4%20w-4%22%7D">
                        <path d="M18 6 6 18"></path>
                        <path d="m6 6 12 12"></path>
                    </svg>
                </button>
                <div class="guidelines-icon">
                    <!-- <i class="fa fa-users"></i> -->
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="white"
                        stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-users h-6 w-6 text-white"
                        data-lov-id="src/components/chat/ChatGuidelines.tsx:39:12" data-lov-name="Users"
                        data-component-path="src/components/chat/ChatGuidelines.tsx" data-component-line="39" data-component-file="ChatGuidelines.tsx" data-component-name="Users" data-component-content="%7B%22className%22%3A%22h-6%20w-6%20text-white%22%7D">
                        <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"></path>
                        <circle cx="9" cy="7" r="4"></circle>
                        <path d="M22 21v-2a4 4 0 0 0-3-3.87"></path>
                        <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
                    </svg>
                </div>
            </div>
            <div class="modal-body">
                <h2>Chat Guidelines</h2>
                <p class="guidelines-subtitle-p">Please follow these guidelines to maintain a healthy learning environment</p>

                <div class="guideline-item">
                    <div class="guideline-icon success">
                        <!-- <i class="fa fa-check"></i> -->
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none"
                            stroke="#3ae478" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                            class="lucide lucide-users h-4 w-4 text-accent" data-lov-id="src/components/chat/ChatGuidelines.tsx:51:16" data-lov-name="guideline.icon" data-component-path="src/components/chat/ChatGuidelines.tsx"
                            data-component-line="51" data-component-file="ChatGuidelines.tsx" data-component-name="guideline.icon" data-component-content="%7B%22className%22%3A%22h-4%20w-4%20text-accent%22%7D">
                            <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"></path>
                            <circle cx="9" cy="7" r="4"></circle>
                            <path d="M22 21v-2a4 4 0 0 0-3-3.87"></path>
                            <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
                        </svg>
                    </div>
                    <div class="guideline-content">
                        <p class="guideline-title">✔️ Maintain proper decorum in class</p>
                        <p class="guideline-description">Be respectful to fellow learners and moderators</p>
                    </div>
                </div>

                <div class="guideline-item">
                    <div class="guideline-icon warning">
                        <!-- <i class="fa fa-exclamation-triangle"></i> -->
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="#3ae478"
                            stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-triangle-alert h-4 w-4 text-accent" data-lov-id="src/components/chat/ChatGuidelines.tsx:51:16" data-lov-name="guideline.icon"
                            data-component-path="src/components/chat/ChatGuidelines.tsx" data-component-line="51" data-component-file="ChatGuidelines.tsx" data-component-name="guideline.icon" data-component-content="%7B%22className%22%3A%22h-4%20w-4%20text-accent%22%7D">
                            <path d="m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3"></path>
                            <path d="M12 9v4"></path>
                            <path d="M12 17h.01"></path>
                        </svg>
                    </div>
                    <div class="guideline-content">
                        <p class="guideline-title">✔️ Inappropriate messages, cursing, or self-promotion is strictly prohibited</p>
                        <p class="guideline-description">Keep discussions relevant to education and exams</p>
                    </div>
                </div>

                <div class="guideline-item">
                    <div class="guideline-icon success">
                        <!-- <i class="fa fa-heart"></i> -->
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="#3ae478" stroke-width="2" stroke-linecap="round"
                            stroke-linejoin="round" class="lucide lucide-shield h-4 w-4 text-accent" data-lov-id="src/components/chat/ChatGuidelines.tsx:51:16" data-lov-name="guideline.icon" data-component-path="src/components/chat/ChatGuidelines.tsx"
                            data-component-line="51" data-component-file="ChatGuidelines.tsx" data-component-name="guideline.icon" data-component-content="%7B%22className%22%3A%22h-4%20w-4%20text-accent%22%7D">
                            <path d="M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z"></path>
                        </svg>
                    </div>
                    <div class="guideline-content">
                        <p class="guideline-title">✔️ Be respectful to other learners</p>
                        <p class="guideline-description">Help create a supportive learning environment</p>
                    </div>
                </div>

                <div class="guideline-item">
                    <div class="guideline-icon info">
                        <!-- <i class="fa fa-shield"></i> -->
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="#3ae478" stroke-width="2"
                            stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-circle-check-big h-4 w-4 text-accent" data-lov-id="src/components/chat/ChatGuidelines.tsx:51:16" data-lov-name="guideline.icon"
                            data-component-path="src/components/chat/ChatGuidelines.tsx" data-component-line="51" data-component-file="ChatGuidelines.tsx" data-component-name="guideline.icon" data-component-content="%7B%22className%22%3A%22h-4%20w-4%20text-accent%22%7D">
                            <path d="M21.801 10A10 10 0 1 1 17 3.335"></path>
                            <path d="m9 11 3 3L22 4"></path>
                        </svg>
                    </div>
                    <div class="guideline-content">
                        <p class="guideline-title">✔️ Refrain from offensive behaviour</p>
                        <p class="guideline-description">Report any inappropriate content to moderators</p>
                    </div>
                </div>

                <div class="safe-space-section">
                    <div class="safe-space-icon">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none"
                            stroke="#2196f3" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                            class="lucide lucide-shield h-4 w-4" data-lov-id="src/components/chat/ChatGuidelines.tsx:67:12" data-lov-name="Shield" data-component-path="src/components/chat/ChatGuidelines.tsx" data-component-line="67" data-component-file="ChatGuidelines.tsx" data-component-name="Shield" data-component-content="%7B%22className%22%3A%22h-4%20w-4%22%7D">
                            <path d="M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z"></path>
                        </svg>
                        <p class="safe-space-title">Safe Learning Space</p>
                    </div>
                    <p class="safe-space-content">Our moderators actively monitor chats to ensure a safe and productive learning environment for all students.</p>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-primary btn-block" id="start-chatting-btn">
                    Got it, let's chat! 🎓
                </button>
            </div>
        </div>
    </div>
</div>

<div id="live-chat-config"
    data-channel-id="<?= $group['channel_id'] ?>"
    data-channel-type="<?= $group['channel_type'] ?>"
    data-entity-type="<?= $group['entity'] ?>"
    data-entity-id="<?= $group['entity_id'] ?>"
    data-current-user='<?= !$loggedInUser ? 'null' : json_encode([
                            'id' => $loggedInUser->id,
                            'name' => $loggedInUser->name ?? 'User'
                        ]) ?>'>
</div>